# Virtual Route Testing Guide

This guide provides comprehensive testing procedures for the virtual route rendering functionality in Flight Landing Pages.

## Overview

Virtual route rendering allows Flight Landing Pages to display dynamic content based on destination pair URLs (e.g., `/flights/ber-sof`). This guide covers testing all aspects of this functionality.

## Prerequisites

Before testing, ensure you have:
1. Flight Landing Pages extension installed and activated
2. Database schema updated (flight routes table and page extensions)
3. At least one Flight Template Page (doktype 200) with content
4. At least one Flight Landing Page (doktype 201) configured
5. Flight routes created and linked to the landing page

## Test Setup

### 1. Create Test Template Page

1. **Create Flight Template Page**:
   - Go to **Web > Page** module
   - Create new page with doktype "Flight Template Page"
   - Title: "Berlin to Sofia Flight Template"
   - Configure **Backend Layout** (e.g., "Two Columns")

2. **Add Content Elements**:
   - Add content to different columns (colPos 0, 1, etc.)
   - Use flight placeholders in content:
     ```
     Header: "Flights from {origin} to {destination}"
     Text: "Book your flight from {origin} to {destination} starting from {price} {currency}."
     ```

### 2. Create Test Landing Page

1. **Create Flight Landing Page**:
   - Go to **Web > Page** module
   - Create new page with doktype "Flight Landing Page"
   - Title: "Flight Search"
   - Slug: "flights"

2. **Configure Landing Page**:
   - **General Tab**: Set title and slug
   - **Configuration Tab**: Select the template page created above
   - **Appearance Tab**: Set "Backend Layout (Subpages)" (e.g., "Three Columns")

### 3. Create Test Flight Routes

1. **Add Flight Routes**:
   - Go to **Web > List** module
   - Navigate to your Flight Landing Page
   - Create new Flight Route records:
     ```
     Route 1:
     - Origin Code: BER
     - Origin Name: Berlin Brandenburg Airport
     - Origin Type: airport
     - Destination Code: SOF
     - Destination Name: Sofia Airport
     - Destination Type: airport
     - Route Slug: ber-sof
     - Is Active: Yes
     
     Route 2:
     - Origin Code: LHR
     - Origin Name: London Heathrow
     - Origin Type: airport
     - Destination Code: JFK
     - Destination Name: John F. Kennedy Airport
     - Destination Type: airport
     - Route Slug: lhr-jfk
     - Is Active: Yes
     ```

## Testing Procedures

### Test 1: Normal Page Access

**Objective**: Verify that Flight Landing Pages render normally when accessed directly.

**Steps**:
1. Visit the landing page directly: `https://yoursite.com/flights`
2. Check page rendering and functionality

**Expected Results**:
- ✅ Page uses site's normal template and styling
- ✅ Page shows normal content elements from the landing page
- ✅ Navigation and footer work normally
- ✅ No flight-specific content is displayed
- ✅ `{flightRouteData.isVirtualRoute}` should be `false` (if debugging)

**Debug Variables** (add to template for testing):
```html
<!-- Debug info (remove in production) -->
<f:if condition="{flightRouteData}">
    <div style="background: #f0f0f0; padding: 10px; margin: 10px; font-family: monospace;">
        <strong>Debug Info:</strong><br>
        Is Virtual Route: {flightRouteData.isVirtualRoute}<br>
        Current Route: {flightRouteData.currentFlightRoute.originCode} → {flightRouteData.currentFlightRoute.destinationCode}<br>
        Template Page UID: {flightRouteData.templatePageUid}<br>
        Backend Layout: {flightRouteData.virtualRouteBackendLayout}
    </div>
</f:if>
```

### Test 2: Virtual Route Access

**Objective**: Verify that virtual routes display template content with flight-specific data.

**Steps**:
1. Visit virtual route: `https://yoursite.com/flights/ber-sof`
2. Check content rendering and data processing

**Expected Results**:
- ✅ Page uses site's template and styling
- ✅ Page shows template page content instead of landing page content
- ✅ Flight-specific placeholders are replaced with actual data
- ✅ Backend layout is applied correctly
- ✅ Navigation and footer still work
- ✅ `{flightRouteData.isVirtualRoute}` should be `true`
- ✅ Dynamic title shows "Flights from Berlin Brandenburg Airport to Sofia Airport"

**Test Different Routes**:
- `https://yoursite.com/flights/lhr-jfk`
- `https://yoursite.com/flights/invalid-route` (should show normal page or 404)

### Test 3: Backend Layout Priority

**Objective**: Verify that backend layout priority logic works correctly.

**Test Scenarios**:

**Scenario A: Landing Page Subpages Layout Set**
1. Configure landing page "Backend Layout (Subpages)" = "Three Columns"
2. Configure template page "Backend Layout" = "Two Columns"
3. Visit virtual route
4. **Expected**: Three Columns layout should be used

**Scenario B: Only Template Page Layout Set**
1. Remove landing page "Backend Layout (Subpages)" setting
2. Keep template page "Backend Layout" = "Two Columns"
3. Visit virtual route
4. **Expected**: Two Columns layout should be used

**Scenario C: No Layout Configured**
1. Remove both layout settings
2. Visit virtual route
3. **Expected**: Default layout should be used

### Test 4: Content Column Rendering

**Objective**: Verify that content is rendered in correct columns based on backend layout.

**Steps**:
1. Add content elements to different columns in template page:
   - Column 0 (main): Header and text content
   - Column 1 (sidebar): Additional information
   - Column 2 (footer): Contact information

2. Use column-based integration in site template:
   ```html
   <div class="main-content">
       <flp:virtualRouteContent colPos="0" />
   </div>
   <div class="sidebar">
       <flp:virtualRouteContent colPos="1" />
   </div>
   <div class="footer-content">
       <flp:virtualRouteContent colPos="2" />
   </div>
   ```

3. Visit virtual route

**Expected Results**:
- ✅ Content appears in correct columns
- ✅ Column positioning matches backend layout
- ✅ Empty columns don't show content
- ✅ Content maintains proper styling

### Test 5: Placeholder Processing

**Objective**: Verify that flight-specific placeholders are correctly replaced.

**Test Content** (add to template page):
```
Header: "Book Flights from {origin} to {destination}"
Text: "
Find the best deals for flights from {origin} ({originCode}) to {destination} ({destinationCode}).

Flight Details:
- Route: {originCode} → {destinationCode}
- Price from: {price} {currency}
- Duration: {flight.duration}
- Airline: {airline}
"
```

**Expected Results**:
- ✅ `{origin}` → "Berlin Brandenburg Airport"
- ✅ `{destination}` → "Sofia Airport"
- ✅ `{originCode}` → "BER"
- ✅ `{destinationCode}` → "SOF"
- ✅ `{price}` → "299" (mock data)
- ✅ `{currency}` → "EUR"
- ✅ `{flight.duration}` → "2h 30m"
- ✅ `{airline}` → "Example Airlines"

### Test 6: Error Handling

**Objective**: Verify proper handling of error conditions.

**Test Cases**:

1. **Invalid Route Pattern**:
   - Visit: `https://yoursite.com/flights/invalid`
   - **Expected**: Normal page content (not virtual route)

2. **Non-existent Route**:
   - Visit: `https://yoursite.com/flights/xyz-abc`
   - **Expected**: Normal page content (route not found)

3. **Inactive Route**:
   - Deactivate a flight route
   - Visit the route URL
   - **Expected**: Normal page content (inactive route ignored)

4. **Missing Template Page**:
   - Remove template page reference from landing page
   - Visit virtual route
   - **Expected**: Normal page content (no template to process)

5. **Hidden Template Page**:
   - Hide the template page
   - Visit virtual route
   - **Expected**: Normal page content (template not accessible)

## Integration Testing

### Test 7: Site Template Integration

**Objective**: Verify integration with different site template approaches.

**Test with Different Integration Methods**:

1. **Partial-Based Integration**:
   ```html
   <f:render partial="FlightContent" arguments="{_all}" />
   ```

2. **Column-Based Integration**:
   ```html
   <flp:virtualRouteContent colPos="0" />
   ```

3. **Conditional Integration**:
   ```html
   <f:if condition="{flightRouteData.isVirtualRoute}">
       <!-- Virtual route content -->
   <f:else>
       <!-- Normal page content -->
   </f:if>
   ```

**Expected Results**:
- ✅ All integration methods work correctly
- ✅ Virtual routes show template content
- ✅ Normal pages show regular content
- ✅ Site styling is preserved

### Test 8: Multi-Site Testing

**Objective**: Verify functionality works correctly in multi-site installations.

**Steps**:
1. Create flight routes in different site contexts
2. Test virtual routes on each site
3. Verify site-specific routing and content

**Expected Results**:
- ✅ Routes are site-specific
- ✅ URLs are generated correctly for each site
- ✅ Content rendering respects site context

## Performance Testing

### Test 9: Caching and Performance

**Objective**: Verify that virtual routes work with TYPO3 caching.

**Steps**:
1. Clear all caches
2. Visit virtual route (cold cache)
3. Visit same route again (warm cache)
4. Visit different virtual route
5. Visit normal page

**Expected Results**:
- ✅ Virtual routes are cached correctly
- ✅ Cache is invalidated when flight data changes
- ✅ Performance is acceptable
- ✅ No cache conflicts between routes

## Troubleshooting Common Issues

### Issue: Virtual Routes Not Working
**Symptoms**: Virtual route URLs show normal page content
**Check**:
- Flight routes are created and active
- Route slugs match URL pattern
- FlightRouteProcessor is configured correctly

### Issue: Content Not Rendering
**Symptoms**: Virtual routes show empty content
**Check**:
- Template page is configured and accessible
- Template page has content elements
- Content elements are not hidden

### Issue: Placeholders Not Replaced
**Symptoms**: Raw placeholder text visible (e.g., "{origin}")
**Check**:
- Flight route data is available
- PlaceholderService is working
- Placeholder syntax is correct

### Issue: Backend Layout Not Applied
**Symptoms**: Content appears in wrong columns
**Check**:
- Backend layout is configured
- Column positions (colPos) are correct
- Site template supports the layout

## Automated Testing

For automated testing, consider creating:
1. **Unit tests** for data processors and services
2. **Functional tests** for virtual route detection
3. **Integration tests** for complete rendering workflow
4. **Performance tests** for caching behavior

## Conclusion

This testing guide ensures that virtual route rendering functionality works correctly across all scenarios. Regular testing helps maintain reliability and catch regressions early in the development process.
