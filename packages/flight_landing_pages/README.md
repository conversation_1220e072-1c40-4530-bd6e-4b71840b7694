# TYPO3 Flight Landing Pages Extension

A TYPO3 extension for creating flight landing pages with configurable airport/city/country pairs and multi-site support. **Integrates seamlessly with your existing site configuration without overriding your templates or TypoScript.**

## Features

### ✈️ **Core Features**
- **Flight Route Configuration**: Configure pairs of 'from' - 'to' locations with codes, names, and types (airport/city/country)
- **Flight Reference List**: Content element to display all available flight routes with filtering options
- **Multi-site Support**: Per-site configuration using site identifiers
- **Template System**: Dynamic content with placeholder replacement
- **Custom Page Types**: Template pages (doktype 200) and landing pages (doktype 201)
- **Site Integration**: Works with your existing site templates and TypoScript configuration

### 🎯 **Key Benefits**
- **SEO-Friendly**: Create structured content for flight routes
- **Scalable**: Easy to add new routes and manage content
- **Flexible**: Multiple display modes (list, grid, cards) and customizable templates
- **Multi-tenant**: Perfect for multi-site TYPO3 installations

## Installation

### Via Composer (Recommended)
```bash
composer require bgs/flight-landing-pages
```

### Manual Installation
1. Download the extension
2. Place it in `packages/flight_landing_pages/`
3. Activate the extension in the Extension Manager

## Site Integration

**Important**: This extension is designed to work **with** your existing site configuration, not replace it.

### Quick Integration

Add flight content to your existing site template:

```html
<!-- In your site's page template -->
<f:render partial="FlightContent" arguments="{_all}" />
```

This will automatically show flight-specific content when viewing virtual routes (e.g., `/flights/ber-sof`) while preserving your site's normal functionality.

### What the Extension Does

- ✅ **Preserves your site's TypoScript and templates**
- ✅ **Only adds flight data processing to Flight Landing Pages (doktype 201)**
- ✅ **Uses your existing page layouts and styling**
- ✅ **Provides flight data via `{flightRouteData}` template variable**

### What the Extension Does NOT Do

- ❌ **Does not override your PAGE object configuration**
- ❌ **Does not replace your site's templates or layouts**
- ❌ **Does not interfere with normal pages**

For detailed integration instructions, see [SITE_INTEGRATION.md](SITE_INTEGRATION.md).

## Configuration

### 1. Database Setup
After installation, update the database schema:
- Go to **Admin Tools > Maintenance > Analyze Database Structure**
- Apply the changes for the flight route table and page extensions

### 2. Create Flight Routes
1. Go to **Web > List** module
2. Create new **Flight Route** records
3. Configure:
   - Origin Code (e.g., "BER")
   - Origin Name (e.g., "Berlin Brandenburg Airport")
   - Origin Type (airport/city/country)
   - Destination Code (e.g., "SOF")
   - Destination Name (e.g., "Sofia Airport")
   - Destination Type (airport/city/country)
   - Route Slug (e.g., "from-BER-to-SOF")

### 3. Add Content Elements
The extension provides two content elements:

#### Flight Page
- Displays flight information with dynamic content
- Configurable template files
- Placeholder replacement for dynamic content

#### Flight Reference List
- Shows all available routes for the current site
- Multiple display modes: list, grid, cards
- Optional origin/destination filters
- Configurable items per page

## Usage

### Flight Route Management
Once flight routes are configured, they can be displayed through:
1. Flight page content elements with dynamic content
2. Flight reference lists showing all available routes
3. Custom templates with placeholder replacement

### URL Generation
Flight landing page URLs are generated in the format: **Site Endpoint + Landing Page Path + Pair Slug**

Example: `https://example.com/flights/from-BER-to-SOF`

#### URL Structure Components:
- **Site Endpoint**: The base URL of your TYPO3 site (e.g., `https://example.com`)
- **Landing Page Path**: The slug/path of the landing page (e.g., `flights`)
- **Pair Slug**: The route identifier (e.g., `from-BER-to-SOF`)

The `UrlGenerationService` handles URL creation and provides methods for:
- Generating URLs from FlightRoute models
- Building URLs from landing page and route IDs
- Parsing URLs to extract components
- Validating URL components

### Template Placeholders
Templates support placeholders that are automatically replaced by the PlaceholderService:

```html
<h1>Flights from {origin} to {destination}</h1>
<p>Price from: {price} {currency}</p>
<p>Duration: {flight.duration}</p>
<p>Departure: {flight.departureTime}</p>
```

Available placeholders (based on flight data structure):
- `{origin}`, `{destination}` - Basic location names
- `{price}`, `{currency}`, `{airline}` - Flight pricing and airline info
- `{departureDate}`, `{returnDate}` - Travel dates
- `{flight.duration}`, `{flight.departureTime}`, `{flight.arrivalTime}` - Nested flight details

### Multi-site Configuration
For multi-site installations:
1. Flight routes are automatically associated with sites based on the page tree where they are stored
2. The FlightReferenceController automatically filters routes by the current site context
3. Configure default templates in TypoScript constants:

```typoscript
plugin.tx_flightlandingpages.settings.defaultTemplate = EXT:your_site/Resources/Private/Templates/Flight/Custom.html
```

## Development

### Extending the Extension
The extension is built with extensibility in mind:

#### Custom Templates
Create custom templates and reference them in:
- FlexForm configuration (FlightPage plugin)
- TypoScript constants

#### Backend Layouts for Template Pages
The extension provides specialized backend layouts designed specifically for Flight Template Pages (doktype 200):

- **Flight Template - Single Column**: Simple single-column layout with header, main content, and footer
- **Flight Template - Two Columns**: Two-column layout with main content and sidebar
- **Flight Template - Hero + Content**: Layout with hero section, header, main content, and footer

These backend layouts are automatically available only for template pages and include:
- Optimized column positions for flight content
- Placeholder-friendly content areas
- Consistent structure for virtual route rendering

To use the backend layouts:
1. Create or edit a Flight Template Page (doktype 200)
2. Go to the **Appearance** tab
3. Select one of the flight-specific backend layouts
4. Add content elements with placeholders as needed

#### Custom Placeholders
Extend the `PlaceholderService` to add custom placeholders:

```php
class CustomPlaceholderService extends \Bgs\FlightLandingPages\Service\PlaceholderService
{
    public function replacePlaceholders(string $template, array $flightData): string
    {
        // Call parent method first
        $template = parent::replacePlaceholders($template, $flightData);

        // Add custom placeholders
        $template = str_replace('{custom.placeholder}', $customValue, $template);

        return $template;
    }
}
```

#### ViewHelper Usage
Use the ReplacePlaceholderViewHelper in your Fluid templates:

```html
<flp:replacePlaceholder content="{templateContent}" data="{flightData}" />
```

#### Custom Flight Data
Extend the `FlightPageController` to integrate with real flight APIs by overriding the `getFlightData` method.

## File Structure

```
flight_landing_pages/
├── Classes/
│   ├── Controller/           # FlightPageController, FlightReferenceController
│   ├── Domain/              # Models (FlightRoute, LandingPage, TemplatePage) & Repositories
│   ├── Service/             # PlaceholderService, SiteConfigurationService
│   └── ViewHelpers/         # ReplacePlaceholderViewHelper
├── Configuration/
│   ├── FlexForms/           # FlightPage.xml, FlightReference.xml
│   ├── TCA/                 # Flight route TCA and page overrides
│   └── TypoScript/          # Constants and setup
├── Resources/
│   ├── Private/             # Templates (FlightPage, FlightReference), Language Files
│   └── Public/              # CSS, JavaScript, Icons
├── composer.json            # Composer configuration
├── ext_emconf.php          # Extension configuration
├── ext_localconf.php       # Plugin registration
├── ext_tables.php          # TCA registration
└── ext_tables.sql          # Database schema
```

## Requirements

- TYPO3 12.4 LTS or higher
- PHP 8.1 or higher (as per TYPO3 12.4 requirements)

## Support

For issues, feature requests, or contributions:
- Create an issue in the project repository
- Check the language files for field descriptions
- Review the TCA configuration for available options

## License

This extension is licensed under GPL-2.0-or-later.

## Credits

Developed following TYPO3 best practices and coding standards.
