<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register new page type icons
    $GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][200] = 'apps-pagetree-flight-template';
    $GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][201] = 'apps-pagetree-flight-landing';

    // Add new page types to doktype select
    $GLOBALS['TCA']['pages']['columns']['doktype']['config']['items'][] = [
        'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.doktype.200',
        'value' => 200,
        'icon' => 'apps-pagetree-flight-template'
    ];
    $GLOBALS['TCA']['pages']['columns']['doktype']['config']['items'][] = [
        'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.doktype.201',
        'value' => 201,
        'icon' => 'apps-pagetree-flight-landing'
    ];

    // Add new fields to pages table
    $tempColumns = [
        'tx_flightlandingpages_template_page' => [
            'exclude' => true,
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.tx_flightlandingpages_template_page',
            'config' => [
                'type' => 'group',
                'allowed' => 'pages',
                'size' => 1,
                'maxitems' => 1,
                'minitems' => 0,
                'suggestOptions' => [
                    'default' => [
                        'additionalSearchFields' => 'title',
                        'addWhere' => ' AND pages.doktype = 200'
                    ]
                ]
            ]
        ],

        'tx_flightlandingpages_cache_lifetime' => [
            'exclude' => true,
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.tx_flightlandingpages_cache_lifetime',
            'config' => [
                'type' => 'number',
                'size' => 10,
                'default' => 3600,
                'range' => [
                    'lower' => 0,
                    'upper' => 86400
                ]
            ]
        ],
        'tx_flightlandingpages_enable_sitemap' => [
            'exclude' => true,
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.tx_flightlandingpages_enable_sitemap',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => '',
                        'value' => '',
                    ]
                ],
                'default' => 0,
            ]
        ]
    ];

    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns('pages', $tempColumns);

    // Template Page Type (200) - Inherit all functionality from standard pages
    $GLOBALS['TCA']['pages']['types'][200] = $GLOBALS['TCA']['pages']['types'][\TYPO3\CMS\Core\Domain\Repository\PageRepository::DOKTYPE_DEFAULT];

    // Customize backend layout field for template pages to show only flight layouts
    $GLOBALS['TCA']['pages']['types'][200]['columnsOverrides']['backend_layout']['config']['itemsProcFunc'] =
        \Bgs\FlightLandingPages\UserFunc\BackendLayoutItemsProcFunc::class . '->getFlightBackendLayouts';
    $GLOBALS['TCA']['pages']['types'][200]['columnsOverrides']['backend_layout_next_level']['config']['itemsProcFunc'] =
        \Bgs\FlightLandingPages\UserFunc\BackendLayoutItemsProcFunc::class . '->getFlightBackendLayouts';

    // Add Page TSconfig for flight-specific backend layouts
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPageTSConfig('
        @import "EXT:flight_landing_pages/Configuration/TsConfig/Page/BackendLayouts.tsconfig"
    ');

    // Landing Page Type (201) - Inherit from standard page and add flight configuration
    // Start with the standard page configuration
    $GLOBALS['TCA']['pages']['types'][201] = $GLOBALS['TCA']['pages']['types'][\TYPO3\CMS\Core\Domain\Repository\PageRepository::DOKTYPE_DEFAULT];

    // Add our flight configuration tab after the general tab
    $GLOBALS['TCA']['pages']['types'][201]['showitem'] = str_replace(
        '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,',
        '--div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,' . "\n" .
        '            --div--;LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.tabs.configuration,
                tx_flightlandingpages_template_page,
                tx_flightlandingpages_cache_lifetime,
                tx_flightlandingpages_enable_sitemap,',
        $GLOBALS['TCA']['pages']['types'][201]['showitem']
    );

    // Add page type restrictions
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addToAllTCAtypes(
        'pages',
        'tx_flightlandingpages_template_page',
        '201',
        'after:slug'
    );


});
