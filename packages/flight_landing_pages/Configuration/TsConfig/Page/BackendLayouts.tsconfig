# Flight Landing Pages Backend Layouts
mod.web_layout.BackendLayouts {
    flight_template_single {
        title = Flight Template - Single Column
        icon = EXT:flight_landing_pages/Resources/Public/Icons/backend-layout-single.svg
        config {
            backend_layout {
                colCount = 1
                rowCount = 3
                rows {
                    1 {
                        columns {
                            1 {
                                identifier = header
                                name = Header
                                colPos = 1
                                colspan = 1
                            }
                        }
                    }
                    2 {
                        columns {
                            1 {
                                identifier = main
                                name = Main Content
                                colPos = 0
                                colspan = 1
                            }
                        }
                    }
                    3 {
                        columns {
                            1 {
                                identifier = footer
                                name = Footer
                                colPos = 2
                                colspan = 1
                            }
                        }
                    }
                }
            }
        }
    }

    flight_template_two_column {
        title = Flight Template - Two Columns
        icon = EXT:flight_landing_pages/Resources/Public/Icons/backend-layout-two-column.svg
        config {
            backend_layout {
                colCount = 2
                rowCount = 3
                rows {
                    1 {
                        columns {
                            1 {
                                identifier = header
                                name = Header
                                colPos = 1
                                colspan = 2
                            }
                        }
                    }
                    2 {
                        columns {
                            1 {
                                identifier = main
                                name = Main Content
                                colPos = 0
                                colspan = 1
                            }
                            2 {
                                identifier = sidebar
                                name = Sidebar
                                colPos = 3
                                colspan = 1
                            }
                        }
                    }
                    3 {
                        columns {
                            1 {
                                identifier = footer
                                name = Footer
                                colPos = 2
                                colspan = 2
                            }
                        }
                    }
                }
            }
        }
    }

    flight_template_hero_content {
        title = Flight Template - Hero + Content
        icon = EXT:flight_landing_pages/Resources/Public/Icons/backend-layout-hero.svg
        config {
            backend_layout {
                colCount = 1
                rowCount = 4
                rows {
                    1 {
                        columns {
                            1 {
                                identifier = hero
                                name = Hero Section
                                colPos = 10
                                colspan = 1
                            }
                        }
                    }
                    2 {
                        columns {
                            1 {
                                identifier = header
                                name = Header
                                colPos = 1
                                colspan = 1
                            }
                        }
                    }
                    3 {
                        columns {
                            1 {
                                identifier = main
                                name = Main Content
                                colPos = 0
                                colspan = 1
                            }
                        }
                    }
                    4 {
                        columns {
                            1 {
                                identifier = footer
                                name = Footer
                                colPos = 2
                                colspan = 1
                            }
                        }
                    }
                }
            }
        }
    }
}
