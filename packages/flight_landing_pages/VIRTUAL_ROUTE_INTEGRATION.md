# Virtual Route Integration Guide

This guide explains how to integrate virtual route rendering functionality for Flight Landing Pages destination pairs.

## Overview

Virtual routes allow Flight Landing Pages to display dynamic content based on URL patterns like `/flights/ber-sof`. When a user visits such a URL, the system:

1. **Uses template page content** as the content source
2. **Applies backend layout** with proper priority logic
3. **Renders as normal TYPO3 page** with all functionality preserved
4. **Processes flight-specific placeholders** in the content

## How Virtual Routes Work

### 1. URL Pattern Detection
The system automatically detects virtual route patterns:
- `/flights/ber-sof` (Berlin to Sofia)
- `/routes/lhr-jfk` (London to New York)
- Any URL ending with `{origin}-{destination}` pattern

### 2. Content Source Priority
When a virtual route is detected:
1. **Template Page**: Content comes from the configured template page
2. **Placeholder Processing**: Flight-specific data replaces placeholders
3. **Backend Layout**: Applied using priority logic (see below)

### 3. Backend Layout Priority
The system determines which backend layout to use:
1. **First Priority**: Landing page "Backend Layout (Subpages)" setting
2. **Fallback**: Template page "Backend Layout" setting
3. **Default**: No specific layout if neither is configured

## Integration Methods

### Method 1: Column-Based Integration (Recommended)

Use the `VirtualRouteContentViewHelper` to render content by column position:

```html
<!-- In your site's page template -->
<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Default" />

<f:section name="Main">
    <div class="container">
        <header>
            <f:render partial="Navigation" arguments="{_all}" />
        </header>

        <main>
            <!-- Main content column (colPos 0) -->
            <div class="main-content">
                <flp:virtualRouteContent colPos="0" />
            </div>

            <!-- Sidebar content (colPos 1) -->
            <div class="sidebar">
                <flp:virtualRouteContent colPos="1" />
            </div>
        </main>

        <footer>
            <f:render partial="Footer" arguments="{_all}" />
        </footer>
    </div>
</f:section>
</html>
```

### Method 2: Partial-Based Integration

Use the provided FlightContent partial for simpler integration:

```html
<!-- In your site's page template -->
<f:section name="Main">
    <div class="container">
        <!-- Flight content (shows template content for virtual routes) -->
        <f:render partial="FlightContent" arguments="{_all}" />

        <!-- Normal page content (shows for non-virtual routes) -->
        <f:if condition="{flightRouteData.isVirtualRoute}">
            <f:else>
                <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
            </f:else>
        </f:if>
    </div>
</f:section>
```

### Method 3: Custom Integration

Build your own integration using the flight route data:

```html
<f:if condition="{flightRouteData.isVirtualRoute}">
    <f:then>
        <!-- Virtual route content -->
        <div class="virtual-route-page">
            <h1>Flights from {flightRouteData.currentFlightRoute.originName} to {flightRouteData.currentFlightRoute.destinationName}</h1>
            
            <!-- Render template content by column -->
            <div class="content-columns">
                <div class="main-column">
                    <flp:virtualRouteContent colPos="0" />
                </div>
                <div class="sidebar-column">
                    <flp:virtualRouteContent colPos="1" />
                </div>
            </div>
        </div>
    </f:then>
    <f:else>
        <!-- Normal page content -->
        <f:cObject typoscriptObjectPath="lib.dynamicContent" data="{colPos: '0'}" />
    </f:else>
</f:if>
```

## Backend Layout Configuration

### 1. Configure Landing Page Layout
1. Edit your Flight Landing Page
2. Go to **Appearance** tab
3. Set **Backend Layout (Subpages)** for virtual routes
4. This layout will be used for all virtual routes of this landing page

### 2. Configure Template Page Layout
1. Edit your Flight Template Page
2. Go to **Appearance** tab
3. Set **Backend Layout** as fallback
4. This layout is used if the landing page doesn't specify a subpages layout

### 3. Layout Priority Example
```
Landing Page: Backend Layout (Subpages) = "Two Columns"
Template Page: Backend Layout = "Single Column"

Result: Virtual routes use "Two Columns" layout
```

## Available Template Variables

### `{flightRouteData}`
Main data object containing:
- `isVirtualRoute` (boolean) - Whether this is a virtual route
- `currentFlightRoute` (array) - Current flight route data
- `templatePageContent` (array) - Processed template content
- `virtualRouteBackendLayout` (string) - Determined backend layout
- `flightData` (array) - Flight information for placeholders

### `{flightRouteData.currentFlightRoute}`
Current route information:
- `originCode`, `originName`, `originType`
- `destinationCode`, `destinationName`, `destinationType`
- `routeSlug`

### `{flightRouteData.flightData}`
Flight information with processed placeholders:
- `origin`, `destination`, `price`, `currency`
- `airline`, `flight.duration`, etc.

## ViewHelper Reference

### `<flp:virtualRouteContent>`
Renders content for a specific column position, automatically handling virtual routes.

**Arguments:**
- `colPos` (integer) - Column position to render (default: 0)
- `flightRouteData` (array) - Optional flight data (taken from template variables if not provided)

**Examples:**
```html
<!-- Render main content column -->
<flp:virtualRouteContent colPos="0" />

<!-- Render sidebar column -->
<flp:virtualRouteContent colPos="1" />

<!-- Render with specific data -->
<flp:virtualRouteContent colPos="0" flightRouteData="{customFlightData}" />
```

### `<flp:contentElement>`
Renders a single content element from array data.

**Arguments:**
- `data` (array) - Content element data array

**Example:**
```html
<f:for each="{flightRouteData.templatePageContent}" as="contentElement">
    <flp:contentElement data="{contentElement}" />
</f:for>
```

## Testing Virtual Routes

### 1. Setup Test Data
1. Create a Flight Template Page with content elements
2. Create a Flight Landing Page and reference the template page
3. Add flight routes (e.g., BER → SOF)
4. Configure backend layouts

### 2. Test Normal Access
Visit the landing page directly: `https://yoursite.com/flights`
- Should show normal page content
- `{flightRouteData.isVirtualRoute}` should be `false`

### 3. Test Virtual Route Access
Visit a virtual route: `https://yoursite.com/flights/ber-sof`
- Should show template page content with flight-specific data
- `{flightRouteData.isVirtualRoute}` should be `true`
- Backend layout should be applied correctly

## Troubleshooting

### Virtual Routes Not Working
1. Check that flight routes are created and active
2. Verify route slugs match URL pattern (`{origin}-{destination}`)
3. Ensure template page is configured and accessible

### Content Not Rendering
1. Verify template page has content elements
2. Check that content elements are not hidden
3. Ensure correct column positions (colPos) are used

### Backend Layout Not Applied
1. Check landing page "Backend Layout (Subpages)" setting
2. Verify template page "Backend Layout" as fallback
3. Ensure your site templates support the selected layouts

### Placeholders Not Replaced
1. Verify flight route data is available
2. Check placeholder syntax in template content
3. Ensure PlaceholderService is working correctly

## Best Practices

1. **Use Column-Based Rendering**: Use `<flp:virtualRouteContent>` for proper backend layout support
2. **Configure Backend Layouts**: Set appropriate layouts for consistent rendering
3. **Test Both Modes**: Test both normal page access and virtual route access
4. **Handle Fallbacks**: Provide fallback content when template data is unavailable
5. **Optimize Performance**: Consider caching for frequently accessed virtual routes
