<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\UserFunc;

use TYPO3\CMS\Backend\Utility\BackendUtility;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * UserFunc to provide backend layout items for flight template pages
 */
class BackendLayoutItemsProcFunc
{
    /**
     * Get flight-specific backend layouts for template pages
     */
    public function getFlightBackendLayouts(array &$config): void
    {
        // Start with default options
        $config['items'] = [
            [
                'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.default_value',
                'value' => '',
                'icon' => null
            ],
            [
                'label' => 'LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:backend_layout.none',
                'value' => '-1',
                'icon' => null
            ]
        ];

        // Add flight-specific backend layouts from Page TSconfig
        $flightLayouts = $this->getFlightBackendLayoutsFromTSconfig();
        foreach ($flightLayouts as $key => $layout) {
            $config['items'][] = [
                'label' => $layout['title'] ?? $key,
                'value' => $key,
                'icon' => $layout['icon'] ?? null
            ];
        }

        // Add flight-specific backend layouts from database records
        $this->addFlightBackendLayoutsFromDatabase($config);
    }

    /**
     * Get flight backend layouts from Page TSconfig
     */
    protected function getFlightBackendLayoutsFromTSconfig(): array
    {
        $pageId = $this->getCurrentPageId();
        if ($pageId === 0) {
            return [];
        }

        $pageTSconfig = BackendUtility::getPagesTSconfig($pageId);
        $backendLayouts = $pageTSconfig['mod.']['web_layout.']['BackendLayouts.'] ?? [];

        $flightLayouts = [];
        foreach ($backendLayouts as $key => $layout) {
            // Only include layouts that start with 'flight_'
            if (strpos($key, 'flight_') === 0 && is_array($layout)) {
                $flightLayouts[$key] = $layout;
            }
        }

        return $flightLayouts;
    }

    /**
     * Add flight backend layouts from database records
     */
    protected function addFlightBackendLayoutsFromDatabase(array &$config): void
    {
        $connection = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('backend_layout');

        $queryBuilder = $connection->createQueryBuilder();
        $result = $queryBuilder
            ->select('uid', 'title', 'icon')
            ->from('backend_layout')
            ->where(
                $queryBuilder->expr()->like(
                    'title',
                    $queryBuilder->createNamedParameter('Flight Template%')
                )
            )
            ->orderBy('title')
            ->executeQuery();

        while ($row = $result->fetchAssociative()) {
            $config['items'][] = [
                'label' => $row['title'],
                'value' => (string)$row['uid'],
                'icon' => $row['icon'] ?: null
            ];
        }
    }

    /**
     * Get current page ID from various sources
     */
    protected function getCurrentPageId(): int
    {
        // Try to get page ID from different sources
        $pageId = 0;

        // From GET/POST parameters
        if (isset($_GET['id'])) {
            $pageId = (int)$_GET['id'];
        } elseif (isset($_POST['data']['pages'])) {
            $pageIds = array_keys($_POST['data']['pages']);
            $pageId = (int)($pageIds[0] ?? 0);
        }

        return $pageId;
    }
}
