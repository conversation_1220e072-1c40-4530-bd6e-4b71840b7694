<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Updates;

use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Install\Updates\DatabaseUpdatedPrerequisite;
use TYPO3\CMS\Install\Updates\UpgradeWizardInterface;

/**
 * Update wizard to create default backend layouts for Flight Landing Pages
 */
class FlightBackendLayoutsUpdateWizard implements UpgradeWizardInterface
{
    public function getIdentifier(): string
    {
        return 'flightLandingPagesBackendLayouts';
    }

    public function getTitle(): string
    {
        return 'Create default backend layouts for Flight Landing Pages';
    }

    public function getDescription(): string
    {
        return 'Creates default backend layouts specifically designed for Flight Template Pages (doktype 200).';
    }

    public function executeUpdate(): bool
    {
        $connection = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('backend_layout');

        $backendLayouts = [
            [
                'title' => 'Flight Template - Single Column',
                'description' => 'Single column layout for flight template pages',
                'config' => $this->getSingleColumnConfig(),
                'icon' => 'EXT:flight_landing_pages/Resources/Public/Icons/backend-layout-single.svg'
            ],
            [
                'title' => 'Flight Template - Two Columns',
                'description' => 'Two column layout with sidebar for flight template pages',
                'config' => $this->getTwoColumnConfig(),
                'icon' => 'EXT:flight_landing_pages/Resources/Public/Icons/backend-layout-two-column.svg'
            ],
            [
                'title' => 'Flight Template - Hero + Content',
                'description' => 'Hero section with content layout for flight template pages',
                'config' => $this->getHeroContentConfig(),
                'icon' => 'EXT:flight_landing_pages/Resources/Public/Icons/backend-layout-hero.svg'
            ]
        ];

        foreach ($backendLayouts as $layout) {
            // Check if layout already exists
            $existingLayout = $connection->select(
                ['uid'],
                'backend_layout',
                ['title' => $layout['title']]
            )->fetchAssociative();

            if (!$existingLayout) {
                $connection->insert('backend_layout', [
                    'pid' => 0, // Store in root
                    'title' => $layout['title'],
                    'description' => $layout['description'],
                    'config' => $layout['config'],
                    'icon' => $layout['icon'],
                    'tstamp' => time(),
                    'crdate' => time()
                ]);
            }
        }

        return true;
    }

    public function updateNecessary(): bool
    {
        $connection = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('backend_layout');

        $count = $connection->count(
            'uid',
            'backend_layout',
            ['title' => 'Flight Template - Single Column']
        );

        return $count === 0;
    }

    public function getPrerequisites(): array
    {
        return [
            DatabaseUpdatedPrerequisite::class
        ];
    }

    private function getSingleColumnConfig(): string
    {
        return 'backend_layout {
    colCount = 1
    rowCount = 3
    rows {
        1 {
            columns {
                1 {
                    identifier = header
                    name = Header
                    colPos = 1
                    colspan = 1
                }
            }
        }
        2 {
            columns {
                1 {
                    identifier = main
                    name = Main Content
                    colPos = 0
                    colspan = 1
                }
            }
        }
        3 {
            columns {
                1 {
                    identifier = footer
                    name = Footer
                    colPos = 2
                    colspan = 1
                }
            }
        }
    }
}';
    }

    private function getTwoColumnConfig(): string
    {
        return 'backend_layout {
    colCount = 2
    rowCount = 3
    rows {
        1 {
            columns {
                1 {
                    identifier = header
                    name = Header
                    colPos = 1
                    colspan = 2
                }
            }
        }
        2 {
            columns {
                1 {
                    identifier = main
                    name = Main Content
                    colPos = 0
                    colspan = 1
                }
                2 {
                    identifier = sidebar
                    name = Sidebar
                    colPos = 3
                    colspan = 1
                }
            }
        }
        3 {
            columns {
                1 {
                    identifier = footer
                    name = Footer
                    colPos = 2
                    colspan = 2
                }
            }
        }
    }
}';
    }

    private function getHeroContentConfig(): string
    {
        return 'backend_layout {
    colCount = 1
    rowCount = 4
    rows {
        1 {
            columns {
                1 {
                    identifier = hero
                    name = Hero Section
                    colPos = 10
                    colspan = 1
                }
            }
        }
        2 {
            columns {
                1 {
                    identifier = header
                    name = Header
                    colPos = 1
                    colspan = 1
                }
            }
        }
        3 {
            columns {
                1 {
                    identifier = main
                    name = Main Content
                    colPos = 0
                    colspan = 1
                }
            }
        }
        4 {
            columns {
                1 {
                    identifier = footer
                    name = Footer
                    colPos = 2
                    colspan = 1
                }
            }
        }
    }
}';
    }
}
