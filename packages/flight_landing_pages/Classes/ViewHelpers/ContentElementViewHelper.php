<?php
namespace Bgs\FlightLandingPages\ViewHelpers;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;

/**
 * ViewHelper to render a content element from array data
 * 
 * Usage:
 * <flp:contentElement data="{contentElementData}" />
 */
class ContentElementViewHelper extends AbstractViewHelper
{
    use CompileWithRenderStatic;

    protected $escapeOutput = false;

    public function initializeArguments()
    {
        $this->registerArgument('data', 'array', 'Content element data array', true);
    }

    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ) {
        $contentData = $arguments['data'];
        
        if (empty($contentData) || !is_array($contentData)) {
            return '';
        }

        // Get the content object renderer from the rendering context
        $request = $renderingContext->getRequest();
        $cObj = $request->getAttribute('currentContentObject');
        
        if (!$cObj instanceof ContentObjectRenderer) {
            $cObj = GeneralUtility::makeInstance(ContentObjectRenderer::class);
        }

        // Render the content element based on its CType
        return self::renderContentElement($cObj, $contentData);
    }

    /**
     * Render a content element based on its data
     */
    protected static function renderContentElement(ContentObjectRenderer $cObj, array $contentData): string
    {
        $cType = $contentData['CType'] ?? 'text';
        
        // Create a new content object renderer for this element
        $elementCObj = GeneralUtility::makeInstance(ContentObjectRenderer::class);
        $elementCObj->start($contentData, 'tt_content');
        
        // Basic rendering based on CType
        switch ($cType) {
            case 'text':
            case 'textmedia':
                return self::renderTextElement($elementCObj, $contentData);
                
            case 'header':
                return self::renderHeaderElement($elementCObj, $contentData);
                
            case 'html':
                return self::renderHtmlElement($elementCObj, $contentData);
                
            case 'image':
                return self::renderImageElement($elementCObj, $contentData);
                
            default:
                // For other content types, try to render using TYPO3's content rendering
                return self::renderGenericElement($elementCObj, $contentData);
        }
    }

    /**
     * Render text/textmedia content element
     */
    protected static function renderTextElement(ContentObjectRenderer $cObj, array $data): string
    {
        $output = '';
        
        // Header
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        // Subheader
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        // Bodytext
        if (!empty($data['bodytext'])) {
            $output .= '<div class="bodytext">' . nl2br(htmlspecialchars($data['bodytext'])) . '</div>';
        }
        
        return '<div class="content-element content-element-' . htmlspecialchars($data['CType']) . '">' . $output . '</div>';
    }

    /**
     * Render header content element
     */
    protected static function renderHeaderElement(ContentObjectRenderer $cObj, array $data): string
    {
        if (empty($data['header'])) {
            return '';
        }
        
        $headerLevel = (int)($data['header_layout'] ?: 1);
        $output = '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        return '<div class="content-element content-element-header">' . $output . '</div>';
    }

    /**
     * Render HTML content element
     */
    protected static function renderHtmlElement(ContentObjectRenderer $cObj, array $data): string
    {
        $output = '';
        
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        if (!empty($data['bodytext'])) {
            $output .= $data['bodytext']; // HTML content is not escaped
        }
        
        return '<div class="content-element content-element-html">' . $output . '</div>';
    }

    /**
     * Render image content element
     */
    protected static function renderImageElement(ContentObjectRenderer $cObj, array $data): string
    {
        $output = '';
        
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        // Basic image rendering - in a real implementation, you'd handle file references
        if (!empty($data['bodytext'])) {
            $output .= '<div class="image-caption">' . htmlspecialchars($data['bodytext']) . '</div>';
        }
        
        return '<div class="content-element content-element-image">' . $output . '</div>';
    }

    /**
     * Render generic content element
     */
    protected static function renderGenericElement(ContentObjectRenderer $cObj, array $data): string
    {
        $output = '';
        
        if (!empty($data['header'])) {
            $headerLevel = (int)($data['header_layout'] ?: 2);
            $output .= '<h' . $headerLevel . '>' . htmlspecialchars($data['header']) . '</h' . $headerLevel . '>';
        }
        
        if (!empty($data['subheader'])) {
            $output .= '<p class="subheader">' . htmlspecialchars($data['subheader']) . '</p>';
        }
        
        if (!empty($data['bodytext'])) {
            $output .= '<div class="bodytext">' . nl2br(htmlspecialchars($data['bodytext'])) . '</div>';
        }
        
        return '<div class="content-element content-element-' . htmlspecialchars($data['CType']) . '">' . $output . '</div>';
    }
}
