<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Configure FlightReference plugin (for content elements)
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::configurePlugin(
        'FlightLandingPages',
        'FlightReference',
        [
            \Bgs\FlightLandingPages\Controller\FlightReferenceController::class => 'list',
        ],
        // non-cacheable actions
        []
    );



    // Register page type icons
    $iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
    $iconRegistry->registerIcon(
        'apps-pagetree-flight-template',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:flight_landing_pages/Resources/Public/Icons/apps-pagetree-flight-template.svg']
    );
    $iconRegistry->registerIcon(
        'apps-pagetree-flight-landing',
        \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
        ['source' => 'EXT:flight_landing_pages/Resources/Public/Icons/apps-pagetree-flight-landing.svg']
    );

    // Register update wizard for backend layouts
    $GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['ext/install']['update']['flightLandingPagesBackendLayouts'] =
        \Bgs\FlightLandingPages\Updates\FlightBackendLayoutsUpdateWizard::class;
});
