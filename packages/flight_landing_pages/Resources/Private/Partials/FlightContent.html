<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:flp="http://typo3.org/ns/Bgs/FlightLandingPages/ViewHelpers"
      data-namespace-typo3-fluid="true">

<!-- 
Flight Content Partial
Include this in your site templates to show flight-specific content

Usage in your site template:
<f:render partial="FlightContent" arguments="{_all}" />

Or with specific flight data:
<f:render partial="FlightContent" arguments="{flightRouteData: flightRouteData}" />
-->

<f:if condition="{flightRouteData.isVirtualRoute}">
    <!-- Virtual Route Content: Replace normal page content with template page content -->
    <div class="flight-virtual-route-content">
        <!-- Flight route header -->
        <div class="flight-route-header">
            <h1>Flights from {flightRouteData.currentFlightRoute.originName} to {flightRouteData.currentFlightRoute.destinationName}</h1>
            <p class="route-info">
                <span class="origin">{flightRouteData.currentFlightRoute.originCode} ({flightRouteData.currentFlightRoute.originType})</span>
                →
                <span class="destination">{flightRouteData.currentFlightRoute.destinationCode} ({flightRouteData.currentFlightRoute.destinationType})</span>
            </p>
        </div>

        <!-- Template page content with processed placeholders -->
        <f:if condition="{flightRouteData.templatePageContent}">
            <div class="flight-template-content">
                <f:for each="{flightRouteData.templatePageContent}" as="contentElement">
                    <!-- Render content element using custom ViewHelper -->
                    <flp:contentElement data="{contentElement}" />
                </f:for>
            </div>
        </f:if>

        <!-- Additional flight information -->
        <f:if condition="{flightRouteData.flightData}">
            <div class="flight-info">
                <h3>Flight Information</h3>
                <div class="flight-details">
                    <p><strong>Route:</strong> {flightRouteData.flightData.origin} → {flightRouteData.flightData.destination}</p>
                    <f:if condition="{flightRouteData.flightData.price}">
                        <p><strong>Starting from:</strong> {flightRouteData.flightData.price} {flightRouteData.flightData.currency}</p>
                    </f:if>
                    <f:if condition="{flightRouteData.flightData.flight.duration}">
                        <p><strong>Flight time:</strong> {flightRouteData.flightData.flight.duration}</p>
                    </f:if>
                </div>
            </div>
        </f:if>
    </div>
</f:if>

</html>
